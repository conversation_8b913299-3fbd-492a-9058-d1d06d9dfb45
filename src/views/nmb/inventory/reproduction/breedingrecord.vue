<template>
  <div class="app-container">
    <div class="stats-container">
      <h3 class="stats-title">繁殖性能分析</h3>
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-label">产犊率</div>
          <div class="stat-value">{{ statsData.calvingRate || '0' }}%</div>
          <div class="stat-desc">(最近30天)</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">成活率</div>
          <div class="stat-value">{{ statsData.liveRate || '0' }}%</div>
          <div class="stat-desc">(最近30天)</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">窝均产仔数</div>
          <div class="stat-value">{{ statsData.wooAverageBirthNumber || '0' }}</div>
          <div class="stat-desc">(最近30天)</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">繁殖周期</div>
          <div class="stat-value">{{ statsData.reproductionCycle || '0' }} 天</div>
          <div class="stat-desc">(最近1年)</div>
        </div>
      </div>
    </div>

    <HeadForm>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="种畜编号" prop="earTagNo">
          <el-input
            v-model="queryParams.earTagNo"
            placeholder="请输入种畜编号"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
      </el-form>
    </HeadForm>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="dataList" border :height="tableHeight">
      <el-table-column type="index" label="序号" align="center" width="60" />
      <el-table-column prop="earTagNo" label="耳标号" align="center" min-width="120" />
      <el-table-column prop="varietiesName" label="品种" align="center" min-width="100" />
      <el-table-column prop="categoryName" label="品类" align="center" min-width="100" />
      <el-table-column prop="breedingTimes" label="配种次数" align="center" min-width="100" />
      <el-table-column prop="calvingTimes" label="分娩次数" align="center" min-width="100" />
      <el-table-column prop="calvingSuccessTimes" label="分娩成功产仔次数" align="center" min-width="140" />
      <el-table-column prop="averageBirthNumber" label="平均产仔数" align="center" min-width="100" />
      <el-table-column prop="liveRate" label="成活率" align="center" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.liveRate }}%
        </template>
      </el-table-column>
      <el-table-column prop="performanceScore" label="性能评分" align="center" min-width="100" />
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { cowBreedStatsList, cowBreedSingleStats } from '@/api/nmb/inventory/index'
import HeadForm from '@/components/HeadForm/index'
import { tableUi } from '@/utils/mixin/tableUi.js'

export default {
  name: "BreedingRecord",
  components: {
    HeadForm
  },
  mixins: [tableUi],
  data() {
    return {
      loading: true,
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: null,
        endTime: null,
        earTagNo: null,
        dateRange: []
      },
      dataList: [],
      total: 0,
      statsData: {}
    }
  },
  created() {
    this.getList()
    this.getStatsData()
  },
  methods: {
    getList() {
      this.loading = true
      if (this.queryParams.dateRange && this.queryParams.dateRange.length === 2) {
        this.queryParams.startTime = this.queryParams.dateRange[0]
        this.queryParams.endTime = this.queryParams.dateRange[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }

      const params = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
        earTagNo: this.queryParams.earTagNo
      }

      cowBreedStatsList(params).then(response => {
        this.dataList = response.result.list || []
        this.total = response.result.total || 0
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    getStatsData() {
      cowBreedSingleStats({}).then(response => {
        if (response.code === 200) {
          this.statsData = response.result || {}
        }
      }).catch(error => {
        console.error('获取统计数据失败:', error)
      })
    },

    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        startTime: null,
        endTime: null,
        earTagNo: null,
        dateRange: []
      }
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.stats-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .stats-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 20px 0;
  }

  .stats-cards {
    display: flex;
    gap: 20px;

    .stat-card {
      flex: 1;
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      border: 1px solid #e9ecef;

      .stat-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 10px;
      }

      .stat-value {
        font-size: 28px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 5px;
      }

      .stat-desc {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

@media (max-width: 768px) {
  .stats-cards {
    flex-direction: column;
    gap: 10px;
  }
}
.el-row{
margin-left: 0 !important;
}
</style>
