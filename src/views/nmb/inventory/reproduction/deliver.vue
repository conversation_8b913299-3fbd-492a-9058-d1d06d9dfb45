<template>
  <div>
    <HeadForm :queryParams="queryParams" ref="queryForm">
      <el-form-item label="母畜编号" prop="earTagNo">
        <el-input
          v-model="queryParams.earTagNo"
          placeholder="请输入母畜编号"
          clearable
          size="small"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="分娩日期" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
    </HeadForm>

    <el-card shadow="never">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addRecord">新增</el-button>
        </el-col>
      </el-row>
      <el-table
        :data="dataList"
        border
        v-loading="loading"
        style="width: 100%"
        height="400"
        v-tableHeight="{ bottomOffset: 69 }"
      >
        <el-table-column type="index" align="center" label="序号" fixed="left" width="60" />
        <el-table-column prop="earTagNo" label="母畜编号" align="center" min-width="120" />
        <el-table-column prop="operateTime" label="分娩日期" align="center" min-width="120">
          <template slot-scope="scope">
            {{ formatDate(scope.row.operateTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalCalves" label="产仔数量" align="center" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.totalCalves || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="aliveCalves" label="公母" align="center" min-width="100">
          <template slot-scope="scope">
            {{ getGenderRatio(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column prop="deadCalves" label="死仔" align="center" min-width="80">
          <template slot-scope="scope">
            {{ scope.row.deadCalves || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="breed" label="品种" align="center" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.breed || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center" min-width="150" />
        <el-table-column prop="deliveryMethod" label="分娩方式" align="center" min-width="100">
          <template slot-scope="scope">
            {{ getDeliveryMethodLabel(scope.row.deliveryMethod) }}
          </template>
        </el-table-column>
        <el-table-column prop="operatePeopleName" label="操作人" align="center" min-width="100" />
      </el-table>
    </el-card>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { calvingPage } from '@/api/nmb/inventory/index'
import HeadForm from '@/components/HeadForm/index'
import { tableUi } from '@/utils/mixin/tableUi.js'

export default {
  mixins: [tableUi],
  components: {
    HeadForm
  },
  data() {
    return {
      loading: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        earTagNo: '',
        startTime: '',
        endTime: '',
        dateRange: []
      },
      dataList: [],
      total: 0
    }
  },
  created() {
  },
  mounted() {
    this.loading = false
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true

      const params = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        earTagNo: this.queryParams.earTagNo,
        startTime: '',
        endTime: ''
      }

      if (this.queryParams.dateRange && this.queryParams.dateRange.length === 2) {
        params.startTime = this.queryParams.dateRange[0]
        params.endTime = this.queryParams.dateRange[1]
      }

      calvingPage(params).then(res => {
        if (res.code === 200) {
          this.dataList = res.result.list || []
          this.total = Number(res.result.total || 0)
        } else {
          this.$message.error(res.message || '获取数据失败')
        }
        this.loading = false
      }).catch(error => {
        console.error('获取列表数据失败:', error)
        this.$message.error('获取数据失败')
        this.loading = false
      })
    },

    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        earTagNo: '',
        startTime: '',
        endTime: '',
        dateRange: []
      }
      this.handleQuery()
    },

    // 重置表单
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },

    // 新增记录
    addRecord() {
      this.$message.info('新增功能待开发')
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '--'
      return dateStr.split(' ')[0] // 只显示日期部分
    },

    // 获取公母比例
    getGenderRatio(row) {
      const male = row.maleCalves || 0
      const female = row.femaleCalves || 0
      if (male === 0 && female === 0) return '--'
      return `${male}/${female}`
    },

    // 获取分娩方式标签
    getDeliveryMethodLabel(value) {
      const methodMap = {
        '1': '助产',
        '2': '难产'
      }
      return methodMap[value] || value || '--'
    }
  }
}
</script>

<style scoped lang='scss'>
</style>
