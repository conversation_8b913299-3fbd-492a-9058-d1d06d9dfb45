<template>
  <div>
    <el-dialog :title="dialogAdd.title" :visible.sync="dialogAdd.open" width="750px" :close-on-click-modal="false"
      @close="refuse" class="siteManagement" append-to-body>
      <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
        <el-form-item label="养殖场性质" prop="pastureNature">
          <el-select v-model="form.pastureNature" clearable class="selectWidth" :disabled="disabled">
            <el-option v-for="dict in dict.type.pasture_nature" :key="dict.label" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="pastureName" label="养殖场名称">
              <el-input v-model="form.pastureName" placeholder="请输入养殖场名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="养殖场地址" prop="area">
              <el-cascader class="inputWidth" :options="areaProps" placeholder="请选择养殖场地址" ref="city"
                @change="goodsSourceChange" v-model="form.area"></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="address" label="详细地址">
              <el-input v-model="form.address" type="text" maxlength="50" placeholder="请输入详细地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="养殖方式" prop="cultivateType">
              <el-select v-model="form.cultivateType" clearable class="selectWidth" :disabled="disabled">
                <el-option v-for="dict in dict.type.cultivate_type" :key="dict.label" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="养殖场照片" prop="farmImages">
              <el-upload
                class="upload-demo"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :on-success="handleUploadSuccess"
                :on-remove="handleRemove"
                :before-upload="beforeUpload"
                :file-list="fileList"
                list-type="picture-card"
                :limit="3"
                :on-exceed="handleExceed"
                :disabled="disabled"
              >
                <i class="el-icon-plus"></i>
                <div slot="tip" class="el-upload__tip">最多上传3张图片，单张图片不超过2M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="refuse">关闭</el-button>
        <el-button type="primary" @click="submitForm" :disabled="btnDisabled">{{ btnDisabled ? '提交中' : '提交' }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getEnterpriseInfo } from "@/api/ffs/supervisionSheet/livingSupervisionApi.js";
import provincialLevel from '@/utils/provincialLevel.js'
import {
  pastureAdd,
  pastureEdit,
  pastureById,
} from "@/api/nmb/inventory/index.js";
import { loopArry } from '@/utils/getaeraDataRange'
export default {
  name: "parteaddModel",
  dicts: ["pasture_nature", "cultivate_type"],
  props: {
    dialogAdd: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    const isMobile = (rule, value, callback) => {
      if (!/^1[3456789]\d{9}$/.test(value)) {
        callback(new Error("请输入正确有效的手机号"));
      } else {
        callback();
      }
    };
    return {
      btnDisabled: false,
      disabled: false,
      companyType: [
        { text: "养殖企业", value: "1" },
        { text: "屠宰企业", value: "2" },
        { text: "银行", value: "3" },
        { text: "保险公司", value: "4" },
        { text: "行业协会", value: "5" },
        { text: "物流公司", value: "6" },
        { text: "个体工商户", value: "7" },
        { text: "合作社", value: "8" },
      ],
      areaProps: provincialLevel.result,
      form: {
        pastureName: "",
        area: undefined,
        address: "",
        pastureNature: "",
        cultivateType: "",
        farmImages: "",
      },
      fileList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + this.$store.getters.token
      },
      userData: {
        empty: false,
      },
      selectList: [],
      rules: {

        corprateName: [
          {
            required: true,
            message: "请输入真实姓名",
            trigger: "blur",
          },
        ],
        pastureNature: [
          {
            required: true,
            message: "请选择养殖场性质",
            trigger: "blur",
          },
        ],
        pastureName: [
          {
            required: true,
            message: "请输入养殖场名称",
            trigger: "blur",
          },
        ],
        address: [
          {
            required: true,
            message: "请填详细地址",
            trigger: "blur",
          },
        ],
        area: [
          {
            required: true,
            message: "请选择养殖场地址",
            trigger: "blur",
          },
        ],
        cultivateType: [
          {
            required: true,
            message: "请选择养殖方式",
            trigger: "change",
          },
        ],
      },
    };
  },
  computed: {
    hangdelType() {
      return (status) => {
        let name = "";
        this.companyType.forEach((item) => {
          if (item.value == status) {
            name = item.text;
          }
        });
        return name;
      };
    },
    labelName() {
      return (val) => {
        if (!val) return;
        if (val.nickName) return val.nickName;
        if (val.userName) return val.userName;
        if (val.corprateName) return val.corprateName;
        if (val.phonenumber) return val.phonenumber;
      };
    },
  },
  created() {
    this.getInfo();
    if (this.dialogAdd.id) {
      this.disabled = true;
    } else {
      this.disabled = false;
    }
  },
  methods: {
    submitForm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        if (this.dialogAdd.id) {
          this.editPasture();
        } else {
          this.addPasture();
        }
      });
    },
    //添加养殖场
    addPasture() {
      this.btnDisabled = true
      pastureAdd(this.form).then((res) => {
        if (res.code == 200) {
          this.btnDisabled = false
          this.$message({
            type: "success",
            message: "保存成功",
          });
          this.$emit("refresh");
          this.refuse();
        }
      }).catch(() => {
        this.btnDisabled = false
      });
    },
    //编辑养殖场
    editPasture() {
      this.btnDisabled = true
      pastureEdit(this.form).then((res) => {
        if (res.code == 200) {
          this.btnDisabled = false
          this.$message({
            type: "success",
            message: "保存成功",
          });
          this.$emit("refresh");
          this.refuse();
        }
      }).catch(() => {
        this.btnDisabled = false
      });
    },
    getInfo() {
      if (!this.dialogAdd.id) {
        return;
      } else {
        pastureById({ ids: [this.dialogAdd.id] }).then((res) => {
          const result = res.result;
          this.form = result;
          this.userData = result;
          this.form.area = [
            this.form.provinceId,
            this.form.cityId,
            this.form.countyId,
          ];

          // 处理图片数据
          if (result.farmImages) {
            const imageUrls = result.farmImages.split(',').filter(url => url.trim())
            this.fileList = imageUrls.map((url, index) => ({
              uid: index,
              name: `image-${index}`,
              status: 'done',
              url: url
            }))
          }
          if (result.pastureNature == 2) {
            getEnterpriseInfo({
              tenantId: result.userId,
            }).then((res) => {
              console.log("qite：", res.result);
              if (res.code == 200) {
                if (res.result) {
                  this.userData = res.result || {};
                  this.userData.empty = false;
                } else {
                  this.form.corprateIdNo = result.corprateIdNo;
                  this.userData = {
                    corprateIdNo: result.corprateIdNo,
                    empty: true,
                  };
                  console.log(" this.userData", this.userData);
                }
              }
            });
          } else {
            console.log(333, result);
          }
        });
      }
    },
    refuse() {
      // 重置表单数据
      this.form = {
        pastureName: "",
        area: undefined,
        address: "",
        pastureNature: "",
        cultivateType: "",
        farmImages: "",
      }
      this.fileList = []
      this.$emit("close");
    },

    //选择省市区
    goodsSourceChange(val) {
      const names = this.$refs["city"].getCheckedNodes()[0].pathLabels;
      console.log(names);
      this.form.provinceName = names[0];
      this.form.provinceId = val[0];
      this.form.cityName = names[1];
      this.form.cityId = val[1];
      if (val.length >= 3) {
        this.form.countyName = names[2];
        this.form.countyId = val[2];
      }
      let list = JSON.parse(JSON.stringify(val))
      list.unshift('0')
      console.log(this.form);
    },

    // 上传成功回调
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.fileList = fileList
        // 将所有图片URL拼接成字符串
        const imageUrls = fileList.map(item => {
          return item.response ? item.response.url : item.url
        }).join(',')
        this.form.farmImages = imageUrls
      } else {
        this.$message.error(response.msg || '上传失败')
        // 移除上传失败的文件
        this.fileList = fileList.filter(item => item.uid !== file.uid)
      }
    },

    // 移除文件
    handleRemove(file, fileList) {
      this.fileList = fileList
      const imageUrls = fileList.map(item => {
        return item.response ? item.response.url : item.url
      }).join(',')
      this.form.farmImages = imageUrls
    },

    // 上传前检查
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },

    // 超出文件数量限制
    handleExceed() {
      this.$message.warning('最多只能上传3张图片')
    },
  },
};
</script>

<style lang="scss">
.siteManagement {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }

  &-info {
    background: #f4f4f4;
    width: 540px;
    margin: 0 0 22px 120px;
    border-radius: 8px;
    padding: 10px 14px;

    &-row {
      padding: 5px 0;
    }
  }
}

.inputWidth {
  width: 100%;
}
</style>
