<template>
  <div>
    <!-- 搜索表单 -->
    <HeadForm :queryParams="queryParams" ref="queryForm">
      <el-form-item label="配种方式" prop="breedingMethod">
        <el-select
          v-model="queryParams.breedingMethod"
          placeholder="请选择配种方式"
          clearable
          size="small"
          style="width: 200px"
        >
          <el-option label="全部" value="" />
          <el-option label="人工授精" value="1" />
          <el-option label="自然交配" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="配种员" prop="operatePeopleName">
        <el-input
          v-model="queryParams.operatePeopleName"
          placeholder="请输入配种员"
          clearable
          size="small"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="配种日期" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="种公畜号" prop="maleEarTagNo">
        <el-input
          v-model="queryParams.maleEarTagNo"
          placeholder="请输入种公畜号"
          clearable
          size="small"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="种母畜号" prop="earTagNo">
        <el-input
          v-model="queryParams.earTagNo"
          placeholder="请输入种母畜号"
          clearable
          size="small"
          style="width: 200px"
        />
      </el-form-item>
    </HeadForm>

    <!-- 表格 -->
    <el-card shadow="never">
      <el-table
        :data="dataList"
        border
        v-loading="loading"
        style="width: 100%"
        height="400"
        v-tableHeight="{ bottomOffset: 69 }"
      >
        <el-table-column type="index" align="center" label="序号" fixed="left" width="60" />
        <el-table-column prop="maleEarTagNo" label="种公畜编号" align="center" min-width="120" />
        <el-table-column prop="earTagNo" label="种母畜编号" align="center" min-width="120" />
        <el-table-column prop="operateTime" label="配种日期" align="center" min-width="120">
          <template slot-scope="scope">
            {{ formatDate(scope.row.operateTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="breedingMethod" label="配种方式" align="center" min-width="100">
          <template slot-scope="scope">
            {{ getBreedingMethodLabel(scope.row.breedingMethod) }}
          </template>
        </el-table-column>
        <el-table-column prop="operatePeopleName" label="配种员" align="center" min-width="100" />
        <el-table-column prop="remark" label="备注" align="center" min-width="150" />
      </el-table>
    </el-card>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { breedingPage } from '@/api/nmb/inventory/index'
import HeadForm from '@/components/HeadForm/index'
import { tableUi } from '@/utils/mixin/tableUi.js'

export default {
  mixins: [tableUi],
  components: {
    HeadForm
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        earTagNo: '',
        startTime: '',
        endTime: '',
        operatePeopleName: '',
        breedingMethod: '',
        maleEarTagNo: '',
        dateRange: []
      },
      // 表格数据
      dataList: [],
      // 总条数
      total: 0
    }
  },
  created() {
  },
  mounted() {
    this.loading = false
    this.getList()
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true

      // 处理时间范围，只传递API支持的参数
      const params = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        earTagNo: this.queryParams.earTagNo,
        startTime: '',
        endTime: ''
      }

      if (this.queryParams.dateRange && this.queryParams.dateRange.length === 2) {
        params.startTime = this.queryParams.dateRange[0]
        params.endTime = this.queryParams.dateRange[1]
      }

      breedingPage(params).then(res => {
        if (res.code === 200) {
          // 对返回的数据进行前端过滤
          let filteredData = res.result.list || []

          // 根据配种方式过滤
          if (this.queryParams.breedingMethod) {
            filteredData = filteredData.filter(item =>
              item.breedingMethod === this.queryParams.breedingMethod
            )
          }

          // 根据配种员过滤
          if (this.queryParams.operatePeopleName) {
            filteredData = filteredData.filter(item =>
              item.operatePeopleName &&
              item.operatePeopleName.includes(this.queryParams.operatePeopleName)
            )
          }

          // 根据种公畜号过滤
          if (this.queryParams.maleEarTagNo) {
            filteredData = filteredData.filter(item =>
              item.maleEarTagNo &&
              item.maleEarTagNo.includes(this.queryParams.maleEarTagNo)
            )
          }

          this.dataList = filteredData
          this.total = Number(res.result.total || 0)
        } else {
          this.$message.error(res.message || '获取数据失败')
        }
        this.loading = false
      }).catch(error => {
        console.error('获取列表数据失败:', error)
        this.$message.error('获取数据失败')
        this.loading = false
      })
    },

    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置按钮操作
    resetQuery() {
      // 重置查询参数到初始状态
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        earTagNo: '',
        startTime: '',
        endTime: '',
        operatePeopleName: '',
        breedingMethod: '',
        maleEarTagNo: '',
        dateRange: []
      }
      this.handleQuery()
    },

    // 重置表单
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '--'
      return dateStr.split(' ')[0] // 只显示日期部分
    },

    // 获取配种方式标签
    getBreedingMethodLabel(value) {
      const methodMap = {
        '1': '人工授精',
        '2': '自然交配'
      }
      return methodMap[value] || value || '--'
    }
  }
}
</script>

<style scoped lang='scss'>
</style>
